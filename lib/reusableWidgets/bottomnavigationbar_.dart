import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/connectivity_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/reusableWidgets/connection_error/no_connection_tag.dart';
import 'package:eljunto/services/setup_locator.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:provider/provider.dart';

import '../constants/common_helper.dart';
import '../models/book_club_model.dart';
import '../models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import '../reusable_api_function/club/club_function.dart';

class BottomNavigationWidget extends StatefulWidget {
  const BottomNavigationWidget({
    super.key,
    required this.navigationShell,
  });

  final StatefulNavigationShell navigationShell;

  @override
  State<BottomNavigationWidget> createState() => _BottomNavigationWidgetState();
}

class _BottomNavigationWidgetState extends State<BottomNavigationWidget> {
  int selectedIndex = 0;
  int? loggedinUserId;
  List<RequestManage>? outgoingRequestList = [];
  List<RequestManage>? pendingInvitations = [];
  List<BookClubModel>? standingBookClubList = [];
  List<BookClubModel>? allBookClubList = [];
  List<BookClubModel>? impromptuBookClubList = [];
  bool isReopened = false;
  bool isStandingIncomingRequest = false;
  bool isImpromIncomingRequest = false;
  final provider = locator<ConnectivityProvider>();
  final Set<int> loadedBranches = {0};

  @override
  void initState() {
    super.initState();
    // This is the correct place to initialize data that needs to be
    // available throughout the app's logged-in state.
    _initializeGlobalData();
  }

  // Renamed for clarity
  Future<void> _initializeGlobalData() async {
    loggedinUserId = await CommonHelper.getLoggedInUserId();
    if (mounted && loggedinUserId != null) {
      // **CRUCIAL FIX**: Kick off the message data fetching from here.
      // This ensures the streams are active as long as the bottom nav bar is visible.
      // We use `context.read` as we only need to call this once.
      context.read<MessageController>().initializeMessagesData(loggedinUserId!);

      // The rest of the notification logic also belongs here.
      await Future.wait(
        [
          getPendingInvitations(),
          getOutGoingRequest(),
          getStandingBookClubsByUserId(false),
          getImpromptuBookClubsByUserId(false),
        ],
      );
    }
  }

  void _goToBranch(int index) {
    setState(() {
      selectedIndex = index;
    });
    if (index == widget.navigationShell.currentIndex ||
        loadedBranches.contains(index)) {
      widget.navigationShell.goBranch(
        index,
        initialLocation: index == widget.navigationShell.currentIndex,
      );
    } else if (provider.status == InternetStatus.connected) {
      loadedBranches.add(index);
      widget.navigationShell.goBranch(
        index,
        initialLocation: false,
      );
    } else {
      context.go('/no-network?targetIndex=$index');
    }
  }

  @override
  Widget build(BuildContext context) {
    // This logic to sync the selectedIndex with the route is fine.
    final currentRoute =
        GoRouter.of(context).routerDelegate.currentConfiguration.uri.toString();
    if (currentRoute.startsWith('/home')) {
      selectedIndex = 0;
    } else if (currentRoute.startsWith('/profile')) {
      selectedIndex = 1;
    } else if (currentRoute.startsWith('/messages')) {
      selectedIndex = 2;
    } else if (currentRoute.startsWith('/clubs')) {
      selectedIndex = 3;
    } else if (currentRoute.startsWith('/search')) {
      selectedIndex = 4;
    } else if (currentRoute.startsWith('/settings')) {
      selectedIndex = 5;
    }

    return PopScope(
      canPop: selectedIndex == 0,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) return;
        if (widget.navigationShell.currentIndex != 0) {
          _goToBranch(0);
        } else {
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        body: Stack(
          children: [
            // **CORRECT IMPLEMENTATION**: The body now simply contains the navigation shell.
            // There's no need for a StreamBuilder or Consumer here.
            widget.navigationShell,
            NoConnectionTag(bottomPosition: 8),
          ],
        ),
        bottomNavigationBar: Container(
          // Using a specific height is better than letting it be dynamic.
          height: 100,
          decoration: const BoxDecoration(
            border: Border(
              top: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: Theme(
            data: ThemeData(splashColor: Colors.transparent),
            child: Consumer<MessageController>(
              builder: (context, messageController, child) {
                // This Consumer correctly listens for changes and rebuilds the bar
                // to show/hide notification icons.
                final hasMessageNotification = messageController
                    .isUnSeenClubMessages; // Using specific flag for messages
                final hasClubNotification =
                    messageController.hasNewNotifications;

                return BottomNavigationBar(
                  type: BottomNavigationBarType.fixed,
                  selectedLabelStyle: lbRegular.copyWith(fontSize: 12),
                  unselectedItemColor: const Color.fromRGBO(0, 0, 0, 1),
                  selectedItemColor: AppConstants.backgroundColor,
                  unselectedLabelStyle: GoogleFonts.libreBaskerville(
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                  showUnselectedLabels: true,
                  currentIndex: selectedIndex,
                  backgroundColor: AppConstants.textGreenColor,
                  onTap: _goToBranch,
                  items: [
                    BottomNavigationBarItem(
                      backgroundColor: AppConstants.textGreenColor,
                      icon: selectedIndex == 0
                          ? Image.asset(
                              "assets/images/bottom_navigation_icon/Home Tan.png",
                              height: 28,
                              width: 38,
                            )
                          : Image.asset(
                              "assets/images/bottom_navigation_icon/Home.png",
                              height: 28,
                              width: 38,
                            ),
                      label: "Home",
                    ),
                    BottomNavigationBarItem(
                      icon: selectedIndex == 1
                          ? Image.asset(
                              "assets/images/bottom_navigation_icon/Profile Tan.png",
                              height: 28,
                              width: 31,
                            )
                          : Image.asset(
                              "assets/images/bottom_navigation_icon/Profile.png",
                              height: 28,
                              width: 31,
                            ),
                      label: "Profile",
                    ),
                    BottomNavigationBarItem(
                      icon: Stack(
                        clipBehavior: Clip.none,
                        children: [
                          selectedIndex == 2
                              ? Image.asset(
                                  "assets/images/bottom_navigation_icon/Messages Tan.png",
                                  height: 28,
                                  width: 38,
                                )
                              : Image.asset(
                                  "assets/images/bottom_navigation_icon/Messages.png",
                                  height: 28,
                                  width: 38,
                                ),
                          if (hasMessageNotification) notificationIcon(),
                        ],
                      ),
                      label: "Messages",
                    ),
                    BottomNavigationBarItem(
                      icon: Stack(
                        clipBehavior: Clip.none,
                        children: [
                          selectedIndex == 3
                              ? Image.asset(
                                  "assets/images/bottom_navigation_icon/Clubs Tan.png",
                                  height: 31,
                                  width: 38,
                                )
                              : Image.asset(
                                  "assets/images/bottom_navigation_icon/Clubs.png",
                                  height: 31,
                                  width: 38,
                                ),
                          if (hasClubNotification) notificationIcon(),
                        ],
                      ),
                      label: "Clubs",
                    ),
                    BottomNavigationBarItem(
                      icon: selectedIndex == 4
                          ? Image.asset(
                              "assets/images/bottom_navigation_icon/Search Tan.png",
                              height: 28,
                              width: 28,
                            )
                          : Image.asset(
                              "assets/images/bottom_navigation_icon/Search.png",
                              height: 28,
                              width: 28,
                            ),
                      label: "Search",
                    ),
                    BottomNavigationBarItem(
                      icon: selectedIndex == 5
                          ? Image.asset(
                              "assets/images/bottom_navigation_icon/Settings Tan.png",
                              height: 28,
                              width: 31,
                            )
                          : Image.asset(
                              "assets/images/bottom_navigation_icon/Settings.png",
                              height: 28,
                              width: 31,
                            ),
                      label: "Settings",
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget notificationIcon() {
    return Positioned(
      top: -6,
      right: 0,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(49),
        child: Image.asset(
          AppConstants.notificationImagePath,
          height: 15,
          width: 15,
          filterQuality: FilterQuality.high,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  Future<void> getPendingInvitations() async {
    if (!mounted) return;
    await Provider.of<BookClubController>(context, listen: false)
        .inComingRequest(
            loggedinUserId ?? 0,
            ClubMembershipStatus.pending,
            ClubMembershipStatus.reOpened,
            ClubRequestType.incomingClubRequestByUserId,
            context)
        .then((value) async {
      if (mounted) {
        pendingInvitations =
            Provider.of<BookClubController>(context, listen: false)
                .incomingOutGoingRequest
                ?.data;
        await Provider.of<MessageController>(context, listen: false)
            .incomingClubInvitationStatus(
                pendingInvitations?.isNotEmpty ?? false);
      }
    });
  }

  Future<void> getOutGoingRequest() async {
    if (!mounted) return;
    await Provider.of<BookClubController>(context, listen: false)
        .inComingRequest(
            loggedinUserId ?? 0,
            ClubMembershipStatus.pending,
            ClubMembershipStatus.reOpened,
            ClubRequestType.outgoingClubRequestByUserId,
            context)
        .then((value) async {
      if (mounted) {
        outgoingRequestList =
            Provider.of<BookClubController>(context, listen: false)
                .incomingOutGoingRequest
                ?.data;
      }
      bool hasNewOutgoingRequests = await checkStatus();
      if (mounted) {
        await Provider.of<MessageController>(context, listen: false)
            .updateOutgoingRequests(hasNewOutgoingRequests);
      }
    });
  }

  Future<bool> checkStatus() async {
    if (outgoingRequestList?.isNotEmpty ?? false) {
      for (var element in outgoingRequestList!) {
        if (element.status == "REOPENED") {
          return true;
        }
      }
    }
    return false;
  }

  Future<void> getStandingBookClubsByUserId(bool isMore) async {
    if (!mounted) return;
    await Provider.of<ClubController>(context, listen: false)
        .getBookClubsByUserId(
            context, loggedinUserId ?? 0, ClubType.standing, isMore)
        .then((value) async {
      if (mounted) {
        standingBookClubList =
            Provider.of<ClubController>(context, listen: false)
                .standingBookClubList;
      }
      bool hasNewStandingRequests = standingBookClubList?.any((element) =>
              element.incomingRequest == true &&
              element.userId == loggedinUserId) ??
          false;
      if (mounted) {
        await Provider.of<MessageController>(context, listen: false)
            .updateStandingClubRequests(hasNewStandingRequests);
      }
    });
  }

  void checkRequest(String clubType) {
    if (clubType == ClubType.standing) {
      isStandingIncomingRequest = standingBookClubList
              ?.any((element) => element.incomingRequest == true) ??
          false;
    } else {
      isImpromIncomingRequest = impromptuBookClubList
              ?.any((element) => element.incomingRequest == true) ??
          false;
    }
  }

  Future<void> getImpromptuBookClubsByUserId(bool isMore) async {
    if (!mounted) return;
    await Provider.of<ClubController>(context, listen: false)
        .getBookClubsByUserId(
            context, loggedinUserId ?? 0, ClubType.impromptu, isMore)
        .then((value) async {
      if (mounted) {
        impromptuBookClubList =
            Provider.of<ClubController>(context, listen: false)
                .impromptuBookClubList;
      }
      bool hasNewImpromptuRequests = impromptuBookClubList?.any((element) =>
              element.incomingRequest == true &&
              element.userId == loggedinUserId) ??
          false;
      if (mounted) {
        await Provider.of<MessageController>(context, listen: false)
            .updateImpromptuClubRequests(hasNewImpromptuRequests);
      }
      checkRequest(ClubType.impromptu);
    });
  }
}
