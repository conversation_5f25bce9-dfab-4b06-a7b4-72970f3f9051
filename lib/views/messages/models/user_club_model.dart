/// Model for user club data from Firebase Firestore
/// Represents the club information stored in users/{userId}/clubs
class UserClubModel {
  final int? bookClubId;
  final String? bookClubName;

  UserClubModel({
    this.bookClubId,
    this.bookClubName,
  });

  factory UserClubModel.fromMap(Map<String, dynamic> map) {
    return UserClubModel(
      bookClubId: map['bookClubId'] as int?,
      bookClubName: map['bookClubName'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'bookClubId': bookClubId,
      'bookClubName': bookClubName,
    };
  }

  @override
  String toString() {
    return 'UserClubModel(bookClubId: $bookClubId, bookClubName: $bookClubName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserClubModel &&
        other.bookClubId == bookClubId &&
        other.bookClubName == bookClubName;
  }

  @override
  int get hashCode => bookClubId.hashCode ^ bookClubName.hashCode;
}
